<!-- 简单的活动监控测试页面 -->
<template>
  <div class="min-h-screen bg-gradient-to-br from-gray-800 to-gray-900 flex flex-col items-center justify-center p-8">
    <!-- 页面标题 -->
    <div class="text-center mb-8">
      <h1 class="text-4xl font-bold text-white mb-4">活动监控功能测试</h1>
      <p class="text-gray-300 text-lg">测试停止和重新开始活动监控功能</p>
    </div>

    <!-- 状态显示 -->
    <div class="bg-white bg-opacity-10 backdrop-blur-sm rounded-2xl p-6 mb-8 w-full max-w-lg">
      <div class="text-center">
        <h2 class="text-xl font-bold text-blue-300 mb-4">监控状态</h2>
        
        <!-- 状态指示器 -->
        <div class="flex items-center justify-center mb-4">
          <div 
            :class="[
              'w-4 h-4 rounded-full mr-3 transition-all duration-300',
              isMonitorActive ? 'bg-green-400 animate-pulse' : 'bg-red-400'
            ]"
          ></div>
          <span class="text-white text-lg">
            {{ isMonitorActive ? '监控运行中' : '监控已停止' }}
          </span>
        </div>

        <!-- 倒计时显示 -->
        <div v-if="countdown > 0" class="mb-4">
          <div class="text-3xl font-mono font-bold text-yellow-300 mb-2">
            {{ countdown }}
          </div>
          <div class="text-gray-300">倒计时剩余秒数</div>
        </div>
      </div>
    </div>

    <!-- 控制按钮 -->
    <div class="bg-white bg-opacity-10 backdrop-blur-sm rounded-2xl p-6 mb-8 w-full max-w-lg">
      <h2 class="text-xl font-bold text-blue-300 mb-4 text-center">测试控制</h2>
      
      <div class="space-y-4">
        <!-- 开始倒计时按钮 -->
        <button
          @click="startCountdown"
          :disabled="countdown > 0"
          class="w-full px-6 py-3 bg-gradient-to-r from-purple-500 to-purple-600 hover:from-purple-600 hover:to-purple-700 disabled:from-gray-500 disabled:to-gray-600 disabled:cursor-not-allowed text-white rounded-lg shadow-lg transition-all duration-300 transform hover:scale-105 disabled:hover:scale-100"
        >
          <i class="fas fa-play mr-2"></i>
          开始倒计时（停止监控）
        </button>

        <!-- 手动启动监控 -->
        <button
          @click="startMonitoring"
          :disabled="isMonitorActive"
          class="w-full px-6 py-3 bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 disabled:from-gray-500 disabled:to-gray-600 disabled:cursor-not-allowed text-white rounded-lg shadow-lg transition-all duration-300 transform hover:scale-105 disabled:hover:scale-100"
        >
          <i class="fas fa-play-circle mr-2"></i>
          启动活动监控
        </button>

        <!-- 手动停止监控 -->
        <button
          @click="stopMonitoring"
          :disabled="!isMonitorActive"
          class="w-full px-6 py-3 bg-gradient-to-r from-red-500 to-red-600 hover:from-red-600 hover:to-red-700 disabled:from-gray-500 disabled:to-gray-600 disabled:cursor-not-allowed text-white rounded-lg shadow-lg transition-all duration-300 transform hover:scale-105 disabled:hover:scale-100"
        >
          <i class="fas fa-stop-circle mr-2"></i>
          停止活动监控
        </button>
      </div>
    </div>

    <!-- 日志显示 -->
    <div class="bg-white bg-opacity-10 backdrop-blur-sm rounded-2xl p-6 w-full max-w-lg mb-8">
      <h2 class="text-xl font-bold text-blue-300 mb-4 text-center">操作日志</h2>
      
      <div class="bg-black bg-opacity-30 rounded-lg p-4 h-40 overflow-y-auto">
        <div v-for="(log, index) in logs" :key="index" class="text-sm mb-1">
          <span class="text-gray-400">{{ log.time }}</span>
          <span class="text-white ml-2">{{ log.message }}</span>
        </div>
        <div v-if="logs.length === 0" class="text-gray-400 text-center">
          暂无日志记录
        </div>
      </div>
      
      <button
        @click="clearLogs"
        class="mt-4 w-full px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white rounded-lg transition-colors"
      >
        清空日志
      </button>
    </div>

    <!-- 返回按钮 -->
    <button
      @click="goBack"
      class="px-6 py-3 bg-gradient-to-r from-gray-600 to-gray-700 hover:from-gray-700 hover:to-gray-800 text-white rounded-lg shadow-lg transition-all duration-300 transform hover:scale-105"
    >
      <i class="fas fa-arrow-left mr-2"></i>
      返回首页
    </button>
  </div>
</template>

<script>
import inactivityMonitor from "@/utils/inactivityMonitor";

export default {
  name: "SimpleTest",
  data() {
    return {
      countdown: 0,
      countdownDuration: 10, // 10秒倒计时
      timer: null,
      isMonitorActive: false,
      logs: []
    };
  },
  
  mounted() {
    this.addLog("测试页面加载完成");
    this.updateMonitorStatus();
  },
  
  beforeDestroy() {
    if (this.timer) {
      clearInterval(this.timer);
      this.timer = null;
    }
    this.addLog("页面即将销毁");
  },
  
  methods: {
    // 开始倒计时（模拟存证页面功能）
    startCountdown() {
      this.addLog("🔄 开始倒计时，停止活动监控");
      
      // 停止活动监控
      inactivityMonitor.stop();
      this.updateMonitorStatus();
      
      // 开始倒计时
      this.countdown = this.countdownDuration;
      this.timer = setInterval(() => {
        if (this.countdown > 0) {
          this.countdown--;
        } else {
          clearInterval(this.timer);
          this.timer = null;
          this.handleCountdownEnd();
        }
      }, 1000);
    },
    
    // 倒计时结束处理
    handleCountdownEnd() {
      this.addLog("⏰ 倒计时结束，重新启动活动监控");
      this.countdown = 0;
      
      // 重新启动活动监控
      this.restartInactivityMonitor();
    },
    
    // 重新启动活动监控
    restartInactivityMonitor() {
      this.addLog("🔄 重新启动活动监控");
      
      inactivityMonitor.start(
        () => {
          this.addLog("⚠️ 检测到无活动，触发超时");
          alert("检测到长时间无活动！");
        },
        15000, // 15秒超时
        (remainingSeconds) => {
          this.addLog(`⚠️ 警告：${remainingSeconds}秒后超时`);
        },
        5000 // 5秒警告时间
      );
      this.updateMonitorStatus();
    },
    
    // 手动启动监控
    startMonitoring() {
      this.addLog("▶️ 手动启动活动监控");
      this.restartInactivityMonitor();
    },
    
    // 手动停止监控
    stopMonitoring() {
      this.addLog("⏹️ 手动停止活动监控");
      inactivityMonitor.stop();
      this.updateMonitorStatus();
    },
    
    // 更新监控状态
    updateMonitorStatus() {
      this.isMonitorActive = inactivityMonitor.isEnabled;
    },
    
    // 添加日志
    addLog(message) {
      const now = new Date();
      const time = now.toLocaleTimeString();
      this.logs.unshift({
        time,
        message
      });
      
      if (this.logs.length > 20) {
        this.logs = this.logs.slice(0, 20);
      }
    },
    
    // 清空日志
    clearLogs() {
      this.logs = [];
      this.addLog("日志已清空");
    },
    
    // 返回首页
    goBack() {
      this.addLog("返回首页");
      this.$router.push("/");
    }
  }
};
</script>

<style scoped>
.overflow-y-auto::-webkit-scrollbar {
  width: 4px;
}

.overflow-y-auto::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 10px;
}

.overflow-y-auto::-webkit-scrollbar-thumb {
  background: rgba(59, 130, 246, 0.5);
  border-radius: 10px;
}
</style>
